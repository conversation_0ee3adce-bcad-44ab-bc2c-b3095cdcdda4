import pandas as pd


def calculate_demographic_data(print_data=True):
    # carregando os dados do arquivo CSV e usando pandas para ler o csv
    df = pd.read_csv('adult.data.csv')

    # 1 º Passo: Contagem de pessoas por raça
    # utilizei value_counts() para obter uma série com os nomes das raças como índices e suas respectivas contagens como valores
    race_count = df['race'].value_counts()
    # Removendo o nome da série e do índice para um output mais limpo
    race_count.name = None
    race_count.index.name = None

    # 2. Idade média dos homens
    # Filtrando apenas os registros onde o sexo é 'Male' e calculando a média das idades
    # Arredondando para uma casa decimal conforme especificado
    men_data = df[df['sex'] == 'Male']
    average_age_men = round(men_data['age'].mean(), 1)

    # 3. Porcentagem de pessoas com diploma de bacharel
    # Contando quantas pessoas têm 'Bachelors' na coluna education
    # Calculando a porcentagem em relação ao total de registros
    total_people = len(df)
    bachelors_count = len(df[df['education'] == 'Bachelors'])
    percentage_bachelors = round((bachelors_count / total_people) * 100, 1)

    # 4. Análise de educação avançada vs salário
    # Definindo quais níveis de educação são considerados "avançados"
    advanced_education_levels = ['Bachelors', 'Masters', 'Doctorate']

    # Criando máscaras booleanas para separar pessoas com e sem educação avançada
    higher_education_mask = df['education'].isin(advanced_education_levels)
    higher_education = df[higher_education_mask]
    lower_education = df[~higher_education_mask]

    # Calculando porcentagens de pessoas que ganham >50K em cada grupo
    # Para educação avançada
    higher_ed_rich_count = len(higher_education[higher_education['salary'] == '>50K'])
    higher_education_rich = round((higher_ed_rich_count / len(higher_education)) * 100, 1)

    # Para educação não avançada
    lower_ed_rich_count = len(lower_education[lower_education['salary'] == '>50K'])
    lower_education_rich = round((lower_ed_rich_count / len(lower_education)) * 100, 1)

    # 5. Número mínimo de horas trabalhadas por semana
    # Encontrando o valor mínimo na coluna 'hours-per-week'
    min_work_hours = df['hours-per-week'].min()

    # 6. Porcentagem de pessoas que trabalham o mínimo de horas e ganham >50K
    # Filtrando pessoas que trabalham o número mínimo de horas
    min_workers = df[df['hours-per-week'] == min_work_hours]
    num_min_workers = len(min_workers)

    # Calculando quantos desses ganham >50K
    min_workers_rich = len(min_workers[min_workers['salary'] == '>50K'])
    rich_percentage = round((min_workers_rich / num_min_workers) * 100, 1)

    # 7. País com maior porcentagem de pessoas que ganham >50K
    # Agrupando por país e calculando a porcentagem de pessoas ricas em cada país
    country_stats = df.groupby('native-country').agg({
        'salary': lambda x: (x == '>50K').sum(),  # Conta pessoas ricas
        'age': 'count'  # Conta total de pessoas (usando qualquer coluna)
    }).rename(columns={'salary': 'rich_count', 'age': 'total_count'})

    # Calculando porcentagem para cada país
    country_stats['rich_percentage'] = (country_stats['rich_count'] / country_stats['total_count']) * 100

    # Encontrando o país com maior porcentagem
    max_percentage_idx = country_stats['rich_percentage'].idxmax()
    highest_earning_country = max_percentage_idx
    highest_earning_country_percentage = round(country_stats.loc[max_percentage_idx, 'rich_percentage'], 1)

    # 8. Ocupação mais popular entre pessoas que ganham >50K na Índia
    # Filtrando dados para Índia e pessoas que ganham >50K
    india_rich = df[(df['native-country'] == 'India') & (df['salary'] == '>50K')]

    # Encontrando a ocupação mais comum usando value_counts() e pegando o primeiro (mais comum)
    if len(india_rich) > 0:
        top_IN_occupation = india_rich['occupation'].value_counts().index[0]
    else:
        top_IN_occupation = None

    # DO NOT MODIFY BELOW THIS LINE

    if print_data:
        print("Number of each race:")
        # Formatando o output de forma mais limpa
        for race, count in race_count.items():
            print(f"{race}: {count}")
        print("Average age of men:", average_age_men)
        print(f"Percentage with Bachelors degrees: {percentage_bachelors}%")
        print(f"Percentage with higher education that earn >50K: {higher_education_rich}%")
        print(f"Percentage without higher education that earn >50K: {lower_education_rich}%")
        print(f"Min work time: {min_work_hours} hours/week")
        print(f"Percentage of rich among those who work fewest hours: {rich_percentage}%")
        print("Country with highest percentage of rich:", highest_earning_country)
        print(f"Highest percentage of rich people in country: {highest_earning_country_percentage}%")
        print("Top occupations in India:", top_IN_occupation)

    return {
        'race_count': race_count,
        'average_age_men': average_age_men,
        'percentage_bachelors': percentage_bachelors,
        'higher_education_rich': higher_education_rich,
        'lower_education_rich': lower_education_rich,
        'min_work_hours': min_work_hours,
        'rich_percentage': rich_percentage,
        'highest_earning_country': highest_earning_country,
        'highest_earning_country_percentage':
        highest_earning_country_percentage,
        'top_IN_occupation': top_IN_occupation
    }
